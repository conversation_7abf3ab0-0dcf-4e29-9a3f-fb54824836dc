import AdminLayout from '@/Layouts/AdminLayout'
import React from 'react'
import { useState } from 'react';
import { FaSearch } from 'react-icons/fa';
import { FaRegEdit } from "react-icons/fa";
import SetDomainStatusPopup from './components/SetDomainStatusPopup';
import { MdOutlineFilterAlt } from 'react-icons/md';
import Filter from '@/Components/MarketPlace/Filter';
import SearchInput from '@/Components/Util/SearchInput';
import { router } from '@inertiajs/react';
import { getEventValue } from '@/Util/TargetInputEvent';
import { useEffect } from 'react';
import MarketTable from './components/MarketTable';
import { useRef } from 'react';

export default function MarketPlaceDomains(props) {

    const initialMount = useRef(true);

    const [row, setRow] = useState([]);
    const [page, setPage] = useState(1);
    const [data, setData] = useState([]);
    const [modal, setModal] = useState(false);
    const [domains, setDomains] = useState(props.data.data);
    const [search, setSearch] = useState(route().params.search);    
    const [tempDomains, setTempDomains] = useState(domains);
    const [pageLimit, setPageLimit] = useState(10);
    const [hasSpinner, setSpinner] = useState(false);

    const getStatus = (status) => {
        let color = 'bg-primary';

        if(status.toLowerCase() == 'pending') color = `bg-yellow-500`
        else if(status.toLowerCase() == 'completed') color = `bg-green-500`
        else if(status.toLowerCase() == 'cancelled') color = `bg-red-500`

        return <div className='flex'>
            <span className={`w-2 h-2 mt-1 mr-2 rounded-full ${color}`}> </span>
            <span className='capitalize'>{status}</span>
        </div>
    }

    const handlePopUp = (row) => {
        setRow(row)
        setModal(true)
    }

    const getAction = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-primary px-1 -mt-8'>View History</span>
                <button onClick={() => { }} className='bg-primary rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaSearch className=' font-bold' />
                </button>
            </div>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-green-600 px-1 -mt-8'>Change Status</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-green-700 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaRegEdit className=' font-bold' />
                </button>
            </div> 
        </div>
    }

    const columns = [
        {
            id: 'name',
            name: 'User',
            selector: row => row.name,
            cell: row => <div className='capitalize'>{`${row.first_name} ${row.last_name}`}</div>,
            sortable: true,
            alpha: true
            // width: '150px'
        },
        {
            id: 'domain',
            name: 'Domain',
            selector: row => row.domain,
            cell: row => <div className='lowercase'>{row.domain}</div>,
            sortable: true,
            alpha: true
            // width: '150px'
        },
        {
            id: 'status',
            name: 'Status',
            selector: row => row.status,
            cell: row => getStatus(row.status),
            sortable: true,
            alpha: true
            // width: '170px'
        },
        {
            id: 'price',
            name: 'Price',
            selector: row => parseInt(row.price),
            cell: row => `$${parseInt(row.price)}`,
            sortable: true,
            alpha: true,
            width: '125px',
        },
        {
            id: "Actions",
            name: 'Actions',
            selector: row => row.id,
            cell: row => getAction(row),
            width: '110px',
        },
    ];

    const doSearch = ({search}) => {
        setSearch(search)

        if(search.trim().length <= 0) setTempDomains(data['data'])
        else { setTempDomains(
                data['data'].filter((a) => {
                return (a.domain).includes(search)
            }))
        }
    }

    const handleLimitChange = (e) => {
        paginate(1, parseInt(getEventValue(e)));
        setPageLimit(parseInt(getEventValue(e)));
    }

    const paginate = (page, limit) => {
        setSpinner(true)

        axios.post(route(`domains.get`), { 
            page: page,
            count: limit,
        }).then((a) => {
            setData(a.data.data)
  
            setTempDomains(a.data.data['data'])
            setSpinner(false)
        })
    }

    useEffect(() => {
        if (initialMount.current) {
            initialMount.current = false;
            return;
        }

        paginate(page, pageLimit)
    }, [page])

    useEffect(() => {
        setData(props.data)
        setTempDomains(props.data.data)
    }, [])

    return (
        <AdminLayout>
            <SetDomainStatusPopup
                row={row}
                modal={modal}
                showModal={setModal}
                tempDomains={tempDomains}
                setTempDomains={setTempDomains}
            ></SetDomainStatusPopup>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col px-5 rounded-lg ">
                <div className='flex justify-start'>
                    <div>
                        <div className='text-3xl font-semibold mb-3'>
                            Marketplace Domains
                        </div>
                        <span className='text-gray-500 max-w-lg'>
                            View and Manage Marketplace Domains
                        </span>
                    </div>
                </div>
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px"}}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={pageLimit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[10, 20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="flex justify-between items-center mt-4 pt-4">
                    <div className="flex items-center">
                        <label className="flex items-center">
                            <MdOutlineFilterAlt />
                            <span className="ml-2 text-sm text-gray-600">
                                Filter:
                            </span>
                        </label>
                        <Filter />
                    </div>

                    <SearchInput
                        onSearchChange={doSearch}
                        placeholder="Search domain"
                    />

                </div>

                <MarketTable
                    hasSpinner={hasSpinner}
                    items={tempDomains}
                    columns={columns}
                />
                <div className='flex justify-between'>
                    <span className='text-sm'>Showing: {data.from} - {data.to} of {data.total}</span>
                    <div className='flex gap-3'>
                        <button onClick={() => { setPage((prev) => prev - 1) }} disabled={data.prev_page_url == null} className={`border border-gray-500 p-1 px-2 rounded-lg disabled:border-gray-300 disabled:text-gray-300`}> prev </button>
                        <button onClick={() => { setPage((prev) => prev + 1) }} disabled={data.next_page_url == null} className={`border border-gray-500 p-1 px-2 rounded-lg disabled:border-gray-300 disabled:text-gray-300`}> next </button>
                    </div>
                </div>
            </div>
        </AdminLayout>
    )
}
