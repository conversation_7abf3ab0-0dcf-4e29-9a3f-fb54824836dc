<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MarketPlaceDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => [
                'nullable',
                Rule::in([
                    "User: Asc",
                    "User: Desc",
                    "Price: Asc",
                    "Price: Desc",
                    "Created By: Asc",
                    "Created By: Desc",
                ])
            ],
            'domain' => ['string', 'nullable'],
            'status' => [
                'nullable',
                Rule::in([
                    "Completed",
                    "Cancelled",
                    "Pending",
                    "Pending Hold",
                    "Pending Order",
                    "Transfer Requested",
                ]),
            'search' => ['nullable','string'],
            'oage' => 'nullable|integer|min:1',
            'count' => 'nullable|integer|min:10'
            ]
        ];
    }
}
